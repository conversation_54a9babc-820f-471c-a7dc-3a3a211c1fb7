{"name": "your-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:admin\" \"npm run dev:location\" \"npm run dev:sensor\" \"npm run dev:contact\" --names \"MAIN,ADMIN,SYNC,SENSOR,CONTACT\" --prefix-colors \"cyan,magenta,green,blue,yellow\"", "dev:main": "vite", "dev:admin": "cd ../airsense-admin && npm start", "dev:location": "cd ../location-service && npm run dev", "dev:sensor": "cd ../sensor-data-service && npm run dev", "dev:contact": "cd ../contact-service && npm run dev", "dev:main-only": "vite", "dev:without-sync": "concurrently \"npm run dev:main\" \"npm run dev:admin\" --names \"MAIN,ADMIN\" --prefix-colors \"cyan,magenta\"", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"chart.js": "^4.5.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}