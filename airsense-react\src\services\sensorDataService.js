// Sensor Data Service for Firebase Integration
const SENSOR_DATA_API_BASE = 'http://localhost:3003/api';

class SensorDataService {
  constructor() {
    this.websocket = null;
    this.listeners = new Set();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  // Initialize WebSocket connection for real-time updates
  initializeWebSocket() {
    try {
      this.websocket = new WebSocket('ws://localhost:3003');
      
      this.websocket.onopen = () => {
        console.log('🔌 Connected to sensor data WebSocket');
        this.reconnectAttempts = 0;
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          if (message.type === 'sensor-data-update') {
            this.notifyListeners(message.location, message.data);
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      this.websocket.onclose = () => {
        console.log('🔌 WebSocket connection closed');
        this.attemptReconnect();
      };

      this.websocket.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };
    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error);
      this.attemptReconnect();
    }
  }

  // Attempt to reconnect WebSocket
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.initializeWebSocket();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('❌ Max WebSocket reconnection attempts reached');
    }
  }

  // Add listener for real-time updates
  addListener(callback) {
    this.listeners.add(callback);
    
    // Initialize WebSocket if not already connected
    if (!this.websocket || this.websocket.readyState === WebSocket.CLOSED) {
      this.initializeWebSocket();
    }
  }

  // Remove listener
  removeListener(callback) {
    this.listeners.delete(callback);
    
    // Close WebSocket if no listeners
    if (this.listeners.size === 0 && this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  // Notify all listeners of new data
  notifyListeners(location, data) {
    this.listeners.forEach(callback => {
      try {
        callback(location, data);
      } catch (error) {
        console.error('❌ Error in listener callback:', error);
      }
    });
  }

  // API Methods

  // Get latest sensor data for a location
  async getLatestSensorData(location) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}/latest`);
      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch latest sensor data');
      }
    } catch (error) {
      console.error(`❌ Error fetching latest data for ${location}:`, error);
      throw error;
    }
  }

  // Get historical sensor data for a location
  async getHistoricalSensorData(location, limit = 100) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}/history?limit=${limit}`);
      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch historical sensor data');
      }
    } catch (error) {
      console.error(`❌ Error fetching historical data for ${location}:`, error);
      throw error;
    }
  }

  // Get daily averages for a location
  async getDailyAverages(location, days = 30) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}/daily-averages?days=${days}`);
      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch daily averages');
      }
    } catch (error) {
      console.error(`❌ Error fetching daily averages for ${location}:`, error);
      throw error;
    }
  }

  // Get sensor data for all locations
  async getAllLocationData() {
    const locations = ['deans-office', 'lecture-hall', 'basement'];
    const results = {};
    
    try {
      const promises = locations.map(async (location) => {
        try {
          const data = await this.getLatestSensorData(location);
          results[location] = data;
        } catch (error) {
          console.warn(`⚠️ No data available for ${location}`);
          results[location] = null;
        }
      });
      
      await Promise.all(promises);
      return results;
    } catch (error) {
      console.error('❌ Error fetching all location data:', error);
      throw error;
    }
  }

  // Send sensor data (for ESP32 simulation)
  async sendSensorData(location, sensorData) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sensorData),
      });
      
      const result = await response.json();
      
      if (result.success) {
        return result;
      } else {
        throw new Error(result.error || 'Failed to send sensor data');
      }
    } catch (error) {
      console.error(`❌ Error sending sensor data for ${location}:`, error);
      throw error;
    }
  }

  // Check service health
  async checkHealth() {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE.replace('/api', '')}/health`);
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('❌ Error checking service health:', error);
      throw error;
    }
  }

  // Format sensor data for display
  formatSensorData(data) {
    if (!data) return null;

    return {
      temperature: parseFloat(data.temperature?.toFixed(1)) || 0,
      humidity: parseFloat(data.humidity?.toFixed(1)) || 0,
      co2: Math.round(data.co2) || 0,
      pm25: parseFloat(data.pm25?.toFixed(1)) || 0,
      pm10: parseFloat(data.pm10?.toFixed(1)) || 0,
      gasResistance: Math.round(data.gasResistance) || 0,  // Fixed: use gasResistance instead of voc
      aqi: Math.round(data.aqi) || 0,
      timestamp: data.timestamp,
      location: data.location
    };
  }

  // Get air quality status based on AQI
  getAirQualityStatus(aqi) {
    if (aqi <= 50) return { status: 'Good', color: '#00e400', level: 'good' };
    if (aqi <= 100) return { status: 'Moderate', color: '#ffff00', level: 'moderate' };
    if (aqi <= 150) return { status: 'Unhealthy for Sensitive Groups', color: '#ff7e00', level: 'moderate' };
    if (aqi <= 200) return { status: 'Unhealthy', color: '#ff0000', level: 'poor' };
    if (aqi <= 300) return { status: 'Very Unhealthy', color: '#8f3f97', level: 'poor' };
    return { status: 'Hazardous', color: '#7e0023', level: 'poor' };
  }

  // Convert location key to display name
  getLocationDisplayName(locationKey) {
    const locationNames = {
      'deans-office': "Dean's Office",
      'lecture-hall': 'Lecture Hall',
      'basement': 'Basement'
    };
    return locationNames[locationKey] || locationKey;
  }

  // Cleanup resources
  cleanup() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.listeners.clear();
  }
}

// Export singleton instance
export const sensorDataService = new SensorDataService();
export default sensorDataService;
