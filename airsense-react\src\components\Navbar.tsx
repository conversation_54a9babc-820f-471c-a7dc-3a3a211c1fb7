import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { locationService } from '../utils/locationService';

const Navbar: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // Function to update state from location service and localStorage
    const updateFromStorage = async () => {
      console.log('🔄 Starting updateFromStorage...');

      // Check URL parameters first (backup method)
      const urlParams = new URLSearchParams(window.location.search);
      const urlAdmin = urlParams.get('admin');
      const urlLocation = urlParams.get('location');

      // Get admin status from localStorage
      let adminStatus = localStorage.getItem('isAdmin');

      console.log('📋 Current state:', {
        urlAdmin,
        urlLocation,
        adminStatus,
        currentLocalStorage: localStorage.getItem('userLocation')
      });

      // If URL parameters exist, use them and update localStorage
      if (urlAdmin === 'true' && urlLocation) {
        console.log('Found admin data in URL parameters:', { urlAdmin, urlLocation });
        localStorage.setItem('isAdmin', 'true');
        adminStatus = 'true';

        // Update location via service (which will also update localStorage)
        await locationService.setLocation(urlLocation, true, 'admin');

        // Clean up URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);
      }

      // Force refresh from service to ensure we have the latest data
      console.log('🔄 Calling forceRefresh...');
      const currentLocation = await locationService.forceRefresh();

      console.log('✅ Updating navbar state:', {
        currentLocation,
        adminStatus,
        locationServiceOnline: locationService.isServiceOnline()
      });

      // Update state
      setSelectedLocation(currentLocation);
      setIsAdmin(adminStatus === 'true');
    };

    // Initial load
    updateFromStorage();

    // Add listener for real-time location updates
    const handleLocationUpdate = (newLocation: string) => {
      console.log('📡 Real-time location update received:', newLocation);
      setSelectedLocation(newLocation);
    };

    locationService.addLocationListener(handleLocationUpdate);

    // Listen for storage changes (when admin logs in from another tab/window)
    const handleStorageChange = (e: StorageEvent) => {
      console.log('Storage change detected:', e);
      updateFromStorage();
    };

    // Listen for custom events (for same-tab updates)
    const handleCustomEvent = () => {
      console.log('Custom admin status change event detected');
      updateFromStorage();
    };

    // Listen for focus events (when user switches back to this tab)
    const handleFocus = () => {
      console.log('Window focused, checking admin status and location...');
      updateFromStorage();
    };

    // Listen for page visibility changes (when user returns to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('Page became visible, checking admin status and location...');
        updateFromStorage();
      }
    };

    // Listen for page load events
    const handlePageShow = () => {
      console.log('Page show event, checking admin status and location...');
      updateFromStorage();
    };

    // Listen for beforeunload to save current state
    const handleBeforeUnload = () => {
      console.log('Page unloading, current state:', { selectedLocation, isAdmin });
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('pageshow', handlePageShow);
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Also listen for custom events (for same-tab updates)
    window.addEventListener('adminStatusChanged', handleCustomEvent);
    window.addEventListener('locationChanged', handleCustomEvent);

    // Simple polling check every 1 second as fallback
    const interval = setInterval(() => {
      const currentLocation = localStorage.getItem('userLocation');
      const currentAdmin = localStorage.getItem('isAdmin');

      if (currentLocation !== selectedLocation || (currentAdmin === 'true') !== isAdmin) {
        console.log('Change detected via polling, updating...');
        updateFromStorage();
      }
    }, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('pageshow', handlePageShow);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('adminStatusChanged', handleCustomEvent);
      window.removeEventListener('locationChanged', handleCustomEvent);
      locationService.removeLocationListener(handleLocationUpdate);
      clearInterval(interval);
    };
  }, []); // Run only once on mount



  const handleHomeClick = () => {
    const isAdmin = localStorage.getItem('isAdmin');
    console.log('Home icon clicked. Admin status:', isAdmin);

    if (isAdmin === 'true') {
      console.log('Redirecting admin to location selection...');
      // Admin: redirect to admin location selection in the same tab
      window.location.href = 'http://localhost:3004/location-selection';
    } else {
      console.log('Redirecting regular user to home page...');
      // Regular user: just go to home page (no location clearing for regular users)
      navigate('/');
    }
  };

  const getLocationDisplayName = (value: string) => {
    switch (value) {
      case 'deans-office':
        return "Dean's Office";
      case 'basement':
        return 'Basement';
      case 'lecture-hall':
        return 'Lecture Hall';
      default:
        return value;
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="nav-bar">
      <div className="logo-container">
        <div className="logo-wrapper">
          <img src="/logo.jpg" alt="AirGuard Logo" className="logo" />
        </div>
      </div>

      <button
        className="mobile-menu-toggle"
        onClick={toggleMobileMenu}
        aria-label="Toggle navigation menu"
      >
        <span></span>
        <span></span>
        <span></span>
      </button>

      <div className={`nav-links ${isMobileMenuOpen ? 'mobile-open' : ''}`}>
        <Link
          className={isActive('/') ? 'active' : ''}
          to="/"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          Home
        </Link>
        <Link
          className={isActive('/dashboard') ? 'active' : ''}
          to="/dashboard"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          Dashboard
        </Link>
        <Link
          className={isActive('/comparison') ? 'active' : ''}
          to="/comparison"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          Comparison
        </Link>
        <Link
          className={isActive('/about') ? 'active' : ''}
          to="/about"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          About Us
        </Link>
        <Link
          className={isActive('/contact') ? 'active' : ''}
          to="/contact"
          onClick={() => setIsMobileMenuOpen(false)}
        >
          Contact Us
        </Link>
      </div>

      <div className={location.pathname === '/comparison' ? 'comparison-location-bar' : 'search-container'}>
        <button
          onClick={handleHomeClick}
          className="home-icon-btn"
          title={isAdmin ? "Admin: Change location" : "Go to home page"}
        >
          <img src="/home.png" alt="Home" className="home-icon" />
        </button>

        <div
          className="location-display"
          style={{
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            border: '1px solid #ccc',
            fontSize: '1rem',
            backgroundColor: isAdmin ? '#e8f5e8' : '#f8f9fa',
            color: '#333',
            minWidth: '150px',
            textAlign: 'center',
            cursor: isAdmin ? 'pointer' : 'default'
          }}
          title={
            selectedLocation
              ? `Current location: ${getLocationDisplayName(selectedLocation)}${isAdmin ? ' (Click to change location)' : ' (Admin-only location)'}`
              : isAdmin
                ? 'Click to select location'
                : 'No location selected (Admin required)'
          }
          onClick={isAdmin ? handleHomeClick : undefined}
        >
          📍 {selectedLocation ? getLocationDisplayName(selectedLocation) : 'No Location Set'}
          {isAdmin && <span style={{ marginLeft: '0.5rem', color: '#28a745', fontSize: '0.8rem' }}>👑</span>}
          {!isAdmin && selectedLocation && <span style={{ marginLeft: '0.5rem', color: '#6c757d', fontSize: '0.8rem' }}>🔒</span>}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
