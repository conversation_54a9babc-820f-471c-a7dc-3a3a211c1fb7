# AirSense - Smart Indoor Air Quality Monitoring System

## 🌟 Overview

AirSense is a comprehensive indoor air quality monitoring system that provides real-time tracking, analysis, and comparison of air quality metrics across different locations. This repository contains both the original HTML/CSS/JS implementation and a modern React conversion.

## 📁 Project Structure

```
Air-Sense/
├── airsense-react/          # Main React application (Vite + TypeScript)
├── airsense-admin/          # Admin React application (Create React App)
├── location-service/        # Location synchronization service (Node.js)
├── sensor-data-service/     # Firebase sensor data service (Node.js + Firebase)
├── resources/               # Original HTML/CSS/JS files and assets
├── start-both-apps.bat      # Windows batch startup script
├── start-both-apps.ps1      # PowerShell startup script
├── DEVELOPMENT_SETUP.md     # Detailed development guide
├── README.md               # This file
└── .gitignore             # Git ignore rules
```

## 🚀 AirSense Application Suite

### 🎯 Core Features
- **Real-time Air Quality Monitoring**: Track 9 different air quality metrics + calculated AQI
- **Multi-Machine Location Sync**: Admin-selected locations appear on all machines instantly
- **Admin Authentication System**: Secure admin access with location management
- **Location-based Tracking**: Monitor multiple indoor locations (Dean's Office, Lecture Hall, Basement)
- **Comparison Tool**: Side-by-side comparison of air quality between locations
- **Historical Data Visualization**: Interactive charts and trend analysis
- **Responsive Design**: Mobile-friendly interface across all applications
- **TypeScript**: Full type safety throughout the application

### 🔐 Admin Management System
- **Separate Admin App**: Dedicated admin interface for authentication and location management
- **Cross-Machine Synchronization**: Location changes propagate to all connected machines
- **Persistent Location State**: Last admin-selected location persists across sessions
- **Visual Access Indicators**: Crown icons for admins, lock icons for regular users
- **Admin-Only Location Control**: Regular users cannot change locations

### 🛠️ Tech Stack
- **Main App**: React 18 + TypeScript + Vite
- **Admin App**: React 18 + TypeScript + Create React App
- **Location Service**: Node.js + Express + WebSocket
- **Sensor Data Service**: Node.js + Express + Firebase Realtime Database
- **Styling**: CSS3 + Bootstrap 5
- **Routing**: React Router DOM
- **State Management**: React Hooks + localStorage + WebSocket
- **Real-time Sync**: WebSocket connections + REST API
- **Storage**: Firebase Realtime Database + JSON file storage

### 🚀 Quick Start

#### Option 1: Full System with Multi-Machine Sync (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd Air-Sense

# Install dependencies for all services
cd airsense-react && npm install
cd ../airsense-admin && npm install
cd ../location-service && npm install
cd ../sensor-data-service && npm install

# Start all services with one command
cd ../airsense-react
npm run dev
```

This starts:
- **Main App**: http://localhost:5173/ (or next available port)
- **Admin App**: http://localhost:3004/
- **Location Sync Service**: http://localhost:3002/
- **Sensor Data Service**: http://localhost:3003/

#### Option 2: Without Location Sync
```bash
cd airsense-react
npm run dev:without-sync
```

#### Option 3: Manual Startup
```bash
# Terminal 1 - Main App
cd airsense-react && npm run dev:main-only

# Terminal 2 - Admin App
cd airsense-admin && npm start

# Terminal 3 - Location Service (optional)
cd location-service && npm run dev

# Terminal 4 - Sensor Data Service (Firebase)
cd sensor-data-service && npm run dev
```

### 📋 Available Scripts

#### Main App (airsense-react/)
- `npm run dev` - Start all services (main + admin + location sync)
- `npm run dev:without-sync` - Start main and admin apps only
- `npm run dev:main-only` - Start only the main app
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality

#### Admin App (airsense-admin/)
- `npm start` - Start admin app on port 3001
- `npm run build` - Build admin app for production

#### Location Service (location-service/)
- `npm run dev` - Start with nodemon (auto-restart)
- `npm start` - Start location sync service on port 3002

### 🔐 Admin Access

1. Go to main app: http://localhost:5173/ (or the port shown in terminal)
2. Click "Admin Login" in the footer
3. Login with credentials:
   - **Email**: `<EMAIL>`
   - **Password**: `admin123`
4. Select location (Dean's Office, Lecture Hall, or Basement)
5. Location appears on ALL machines running the website!

### 🎯 User Experience Flow

#### For Regular Users:
- Visit main app → See current admin-selected location in navbar
- Location is read-only (shows lock icon 🔒)
- Cannot change location without admin access

#### For Admins:
- Click "Admin Login" → Enter credentials → Select location
- Location shows with crown icon 👑 indicating admin control
- Home icon redirects back to location selection for easy changes
- All location changes sync across all machines instantly

### Air Quality Metrics

The system monitors the following metrics:

| Metric | Unit | Good Range | Moderate Range | Poor Range |
|--------|------|------------|----------------|------------|
| Temperature | °C | 21.8-26.1 | 18-21.8, 26.1-30 | <18, >30 |
| Humidity | % | 30-60 | 20-30, 60-70 | <20, >70 |
| PM2.5 | µg/m³ | 0-100 | 100-125 | >125 |
| PM10 | µg/m³ | 0-200 | 200-250 | >250 |
| CO2 Level | ppm | 400-800 | 800-1200 | >1200 |
| Pressure | hPa | 980-1020 | 960-980 | <960 |
| Gas Resistance | Ω | 500-1500 | 1500-2500 | >2500 |
| Nitrogen Dioxide | ppb | 0-110 | 110-130 | >130 |
| Ozone | ppb | 0-100 | 100-120 | >120 |
| **AQI** | - | 0-66 | 67-99 | >99 |

## 🌐 Multi-Machine Location Synchronization

### How It Works
The system includes a lightweight Node.js service that enables real-time location synchronization across multiple machines:

1. **Admin changes location** in the admin app
2. **Location service** stores the change and broadcasts to all machines
3. **All connected machines** receive real-time updates via WebSocket
4. **Location persists** across browser sessions and machine restarts

### Key Benefits
- ✅ **Real-time sync** across unlimited machines
- ✅ **Persistent storage** until admin changes location again
- ✅ **Graceful fallback** to localStorage if service is down
- ✅ **Admin-only control** - regular users cannot change location
- ✅ **Visual indicators** - crown for admins, lock for regular users
- ✅ **No database required** - simple JSON file storage

### Testing Multi-Machine Sync
1. Start the full system: `npm run dev`
2. Open multiple browser windows/tabs to the main app URL (shown in terminal)
3. Login as admin in one window (click "Admin Login" in footer)
4. Change location - watch it update in ALL windows instantly!
5. Test on different machines using IP address instead of localhost
6. Fresh page loads will show the last admin-selected location

### API Endpoints

#### Location Service (Port 3002)
- `GET /health` - Service health check
- `GET /api/location` - Get current global location
- `POST /api/location` - Set location (admin only)
- `WebSocket /` - Real-time location updates

#### Sensor Data Service (Port 3003)
- `GET /health` - Service health check
- `POST /api/sensor-data/:location` - ESP32 sensor data ingestion
- `GET /api/sensor-data/:location/latest` - Get latest sensor reading
- `GET /api/sensor-data/:location/history` - Get historical sensor data
- `GET /api/sensor-data/:location/daily-averages` - Get daily averages
- `POST /api/daily-averages/calculate` - Manual daily average calculation
- `WebSocket /` - Real-time sensor data updates

### 🔧 Backend Architecture

#### Location Synchronization Service
- **Technology**: Node.js + Express + WebSocket
- **Storage**: JSON file-based (no database required)
- **Real-time**: WebSocket connections for instant updates
- **Fallback**: localStorage when service unavailable
- **CORS**: Configured for multiple port support
- **Validation**: Admin-only location changes with input validation

#### Key Backend Features
- **Persistent State**: Location persists across server restarts
- **Multi-client Support**: Unlimited concurrent connections
- **Error Handling**: Graceful degradation when service unavailable
- **Logging**: Comprehensive console logging with emojis
- **Health Monitoring**: Built-in health check endpoint

## � Firebase Integration

### Overview
The AirSense system now includes comprehensive Firebase integration for real-time sensor data storage and retrieval. The sensor data service provides a robust backend for ESP32 devices and frontend applications.

### Firebase Configuration
- **Database**: Firebase Realtime Database
- **URL**: `https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/`
- **Project ID**: `test-2-c0fd4`
- **Authentication**: Firebase Admin SDK with service account

### Database Structure
```
sensorData/
├── deans-office/
│   ├── 2025-07-09T13_39_25_719Z/     # Hourly readings
│   ├── 2025-07-09T13_39_26_087Z/
│   └── dailyAverage/
│       ├── 2025-07-09/               # Daily averages
│       └── 2025-07-08/
├── lecture-hall/
└── basement/
```

### Features
- **Real-time Data Storage**: Automatic storage of ESP32 sensor readings
- **Daily Averages**: Automated calculation and storage of daily metrics
- **WebSocket Updates**: Real-time data streaming to frontend applications
- **Data Validation**: Comprehensive validation of sensor readings
- **AQI Calculation**: Automatic Air Quality Index computation
- **Historical Data**: Efficient retrieval of past sensor readings

### ESP32 Integration
ESP32 devices can send sensor data via POST requests:
```javascript
POST /api/sensor-data/:location
Content-Type: application/json

{
  "temperature": 22.5,
  "humidity": 45.2,
  "co2": 850,
  "pm25": 15.3,
  "pm10": 22.1,
  "voc": 250
}
```

### Frontend Integration
React applications can use the provided hooks and services:
```javascript
import { useSensorData } from './hooks/useSensorData';

const { data, loading, error } = useSensorData('deans-office');
```

## �📚 Resources Folder

The `resources/` folder contains the original implementation:

### Original Files
- **HTML Pages**: `home1.html`, `home2.html`, `dashboard.html`, `comparison.html`
- **Stylesheets**: `home.css`, `dashboard.css`, `comparison.css`
- **JavaScript**: `dashboard.js`, `comparison.js`, `test.js`
- **Assets**: All original images and icons

### Purpose
- Reference for the original design and functionality
- Backup of the initial implementation
- Resource for understanding the conversion process

## 🔄 Conversion Details

The React application is a **pixel-perfect, functionally-identical** conversion of the original HTML/CSS/JS files with the following improvements:

### Architecture Improvements
- **Component-based**: Reusable React components
- **Type Safety**: Full TypeScript implementation
- **Modern Tooling**: Vite for fast development and building
- **State Management**: React hooks for data flow
- **Routing**: Client-side navigation with React Router

### Preserved Features
- ✅ Exact visual design and layout
- ✅ All original functionality
- ✅ Location selection and persistence
- ✅ Real-time data simulation
- ✅ Air quality calculations
- ✅ Responsive design
- ✅ Bootstrap carousel and components

## 🛠️ Development

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Installation for New Users

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd Air-Sense/airsense-react
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development**:
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:5173` (or next available port)

### Production Deployment

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Preview the build**:
   ```bash
   npm run preview
   ```

The production files will be in the `dist/` folder.

## 🛠️ Troubleshooting

### Common Issues

#### "localhost refused to connect" for Admin Login
- **Cause**: Admin app not running on expected port
- **Solution**: Check terminal output for actual admin app port, or restart with `npm run dev`

#### Location shows "No Location Set" on fresh load
- **Cause**: Location service not running or connection issue
- **Solution**:
  1. Ensure location service is running: `cd location-service && npm run dev`
  2. Check browser console for connection errors
  3. Verify CORS settings in location-service/server.js

#### Location changes don't sync across tabs/machines
- **Cause**: WebSocket connection issues
- **Solution**:
  1. Check if location service is running on port 3002
  2. Verify WebSocket connections in browser dev tools
  3. Restart all services: `npm run dev`

#### Port conflicts
- **Cause**: Ports already in use
- **Solution**:
  1. Kill existing processes: `Get-Process -Name node | Stop-Process -Force`
  2. Restart services
  3. Check terminal output for actual ports being used

### Manual Startup (if npm run dev fails)
```bash
# Terminal 1 - Location Service
cd location-service
node server.js

# Terminal 2 - Admin App
cd airsense-admin
npm start

# Terminal 3 - Main App
cd airsense-react
npm run dev:main
```

## 🌐 Browser Support

- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers

## 📝 License

This project is part of an academic/research initiative for indoor air quality monitoring.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For questions or support, please refer to the documentation in the `airsense-react/` folder or check the original implementation in the `resources/` folder.
Air Quality Monitoring System
