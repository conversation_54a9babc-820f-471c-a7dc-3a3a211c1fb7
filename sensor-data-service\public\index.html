<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirSense Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .logo {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .admin-panel {
            display: none;
        }

        .admin-panel.active {
            display: block;
        }

        .login-form.hidden {
            display: none;
        }

        .current-settings {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }

        .current-settings h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            font-weight: 500;
            color: #555;
        }

        .setting-value {
            color: #667eea;
            font-weight: 600;
        }

        .logout-btn {
            background: #dc3545;
            margin-top: 20px;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🌬️ AirSense</div>
        <div class="subtitle">Admin Control Panel</div>

        <!-- Login Form -->
        <div id="loginForm" class="login-form">
            <div class="form-group">
                <label for="email">Admin Email</label>
                <input type="email" id="email" placeholder="<EMAIL>" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" placeholder="Enter password" value="admin123">
            </div>
            <button class="btn" onclick="login()">
                <span id="loginLoading" class="loading hidden"></span>
                Login
            </button>
        </div>

        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <div class="current-settings">
                <h3>📊 Current Settings</h3>
                <div class="setting-item">
                    <span class="setting-label">Admin Status:</span>
                    <span class="setting-value" id="adminStatus">Loading...</span>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Data Location:</span>
                    <span class="setting-value" id="currentLocation">Loading...</span>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Sensor_Data Category:</span>
                    <span class="setting-value" id="currentCategory">Loading...</span>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Data Collection:</span>
                    <span class="setting-value" id="dataCollection">Loading...</span>
                </div>
            </div>

            <div class="form-group">
                <label for="locationSelect">📍 Select Data Storage Location</label>
                <select id="locationSelect">
                    <option value="basement">Basement</option>
                    <option value="lecture-hall">Lecture Hall</option>
                    <option value="deans-office">Dean's Office</option>
                </select>
            </div>

            <button class="btn" onclick="updateLocation()">
                <span id="locationLoading" class="loading hidden"></span>
                Update Location
            </button>

            <div class="form-group">
                <label for="categorySelect">📁 Select Sensor_Data Category</label>
                <select id="categorySelect">
                    <option value="Basement">Basement</option>
                    <option value="Lecture Hall">Lecture Hall</option>
                    <option value="Dean's Office">Dean's Office</option>
                </select>
            </div>

            <button class="btn" onclick="updateCategory()">
                <span id="categoryLoading" class="loading hidden"></span>
                Update Category
            </button>

            <button class="btn logout-btn" onclick="logout()">
                <span id="logoutLoading" class="loading hidden"></span>
                Logout
            </button>
        </div>

        <!-- Status Messages -->
        <div id="statusMessage" class="status hidden"></div>
    </div>

    <script>
        let currentAdmin = null;

        // Check if already logged in on page load
        window.onload = function() {
            checkLoginStatus();
        };

        async function checkLoginStatus() {
            try {
                const response = await fetch('/api/admin/status');
                const data = await response.json();
                
                if (data.success && data.status.session.isActive) {
                    currentAdmin = data.status.session.adminEmail;
                    showAdminPanel();
                    updateCurrentSettings(data.status);
                }
            } catch (error) {
                console.error('Error checking login status:', error);
            }
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showStatus('Please enter both email and password', 'error');
                return;
            }

            showLoading('loginLoading', true);
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (data.success) {
                    currentAdmin = email;
                    showStatus('Login successful!', 'success');
                    showAdminPanel();
                    loadCurrentSettings();
                } else {
                    showStatus(data.error || 'Login failed', 'error');
                }
            } catch (error) {
                showStatus('Network error. Please try again.', 'error');
                console.error('Login error:', error);
            } finally {
                showLoading('loginLoading', false);
            }
        }

        async function logout() {
            showLoading('logoutLoading', true);
            
            try {
                const response = await fetch('/api/admin/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: currentAdmin })
                });

                const data = await response.json();
                
                if (data.success) {
                    showStatus('Logged out successfully', 'info');
                    showLoginForm();
                    currentAdmin = null;
                } else {
                    showStatus(data.error || 'Logout failed', 'error');
                }
            } catch (error) {
                showStatus('Network error during logout', 'error');
                console.error('Logout error:', error);
            } finally {
                showLoading('logoutLoading', false);
            }
        }

        async function updateLocation() {
            const location = document.getElementById('locationSelect').value;
            
            showLoading('locationLoading', true);
            
            try {
                const response = await fetch('/api/admin/location', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        location: location,
                        adminEmail: currentAdmin 
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showStatus(`Location updated to: ${location}`, 'success');
                    loadCurrentSettings();
                } else {
                    showStatus(data.error || 'Failed to update location', 'error');
                }
            } catch (error) {
                showStatus('Network error updating location', 'error');
                console.error('Location update error:', error);
            } finally {
                showLoading('locationLoading', false);
            }
        }

        async function updateCategory() {
            const category = document.getElementById('categorySelect').value;
            
            showLoading('categoryLoading', true);
            
            try {
                const response = await fetch('/api/admin/sensor-data-category', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        category: category,
                        adminEmail: currentAdmin 
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showStatus(`Sensor_Data category updated to: ${category}`, 'success');
                    loadCurrentSettings();
                } else {
                    showStatus(data.error || 'Failed to update category', 'error');
                }
            } catch (error) {
                showStatus('Network error updating category', 'error');
                console.error('Category update error:', error);
            } finally {
                showLoading('categoryLoading', false);
            }
        }

        async function loadCurrentSettings() {
            try {
                const response = await fetch('/api/admin/status');
                const data = await response.json();
                
                if (data.success) {
                    updateCurrentSettings(data.status);
                }
            } catch (error) {
                console.error('Error loading current settings:', error);
            }
        }

        function updateCurrentSettings(status) {
            document.getElementById('adminStatus').textContent = 
                status.session.isActive ? 'Online' : 'Offline';
            document.getElementById('currentLocation').textContent = 
                status.session.activeLocation.location || 'Not set';
            document.getElementById('currentCategory').textContent = 
                status.activeSensorDataCategory || 'Not set';
            document.getElementById('dataCollection').textContent = 
                status.session.activeLocation.isCollecting ? 'Active' : 'Inactive';
                
            // Update select values
            document.getElementById('locationSelect').value = 
                status.session.activeLocation.location || 'basement';
            document.getElementById('categorySelect').value = 
                status.activeSensorDataCategory || 'Basement';
        }

        function showAdminPanel() {
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('adminPanel').classList.add('active');
        }

        function showLoginForm() {
            document.getElementById('adminPanel').classList.remove('active');
            document.getElementById('loginForm').classList.remove('hidden');
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.classList.remove('hidden');
            
            setTimeout(() => {
                statusEl.classList.add('hidden');
            }, 5000);
        }

        function showLoading(elementId, show) {
            const loadingEl = document.getElementById(elementId);
            if (show) {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }
    </script>
</body>
</html>
