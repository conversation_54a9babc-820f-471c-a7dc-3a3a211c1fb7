import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/LocationSelection.css';

const LocationSelection: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const navigate = useNavigate();

  const getLocationDisplayName = (value: string) => {
    switch (value) {
      case 'deans-office':
        return "Dean's Office";
      case 'basement':
        return 'Basement';
      case 'lecture-hall':
        return 'Lecture Hall';
      default:
        return value;
    }
  };

  useEffect(() => {
    // Check if user is authenticated as admin
    const isAdmin = localStorage.getItem('isAdmin');
    if (!isAdmin) {
      navigate('/');
      return;
    }

    // Add location-selection-page class to body for background styling
    document.body.classList.add('location-selection-page');
    
    // Cleanup function to remove class when component unmounts
    return () => {
      document.body.classList.remove('location-selection-page');
    };
  }, [navigate]);

  const handleLocationChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const location = event.target.value;
    setSelectedLocation(location);
  };

  const handleSubmit = async () => {
    if (selectedLocation) {
      console.log('Admin selected location:', selectedLocation);

      // Store admin status in localStorage
      localStorage.setItem('isAdmin', 'true');

      try {
        // Call the database service to set location and start data collection
        const dbResponse = await fetch('http://localhost:3003/api/admin/location', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            location: selectedLocation,
            adminEmail: '<EMAIL>'
          })
        });

        if (dbResponse.ok) {
          const dbData = await dbResponse.json();
          console.log('✅ Location set in database:', dbData.message);
        } else {
          console.log('⚠️ Database service error, continuing with sync service');
        }

        // Update location via sync service (for UI synchronization)
        const syncResponse = await fetch('http://localhost:3002/api/location', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            location: selectedLocation,
            isAdmin: true,
            adminId: '<EMAIL>'
          })
        });

        if (syncResponse.ok) {
          const syncData = await syncResponse.json();
          if (syncData.success) {
            console.log('✅ Location updated via sync service:', selectedLocation);
          } else {
            console.log('⚠️ Sync service rejected update, falling back to localStorage');
            localStorage.setItem('userLocation', selectedLocation);
          }
        } else {
          console.log('⚠️ Sync service unavailable, falling back to localStorage');
          localStorage.setItem('userLocation', selectedLocation);
        }
      } catch (error) {
        console.log('⚠️ Service error, falling back to localStorage:', error);
        localStorage.setItem('userLocation', selectedLocation);
      }

      // Dispatch events for backward compatibility
      window.dispatchEvent(new Event('adminStatusChanged'));
      window.dispatchEvent(new Event('locationChanged'));

      console.log('📍 Admin selected location and data collection started:', selectedLocation);
      console.log('Redirecting to main application...');

      // Add a small delay to ensure localStorage is saved and events are dispatched
      setTimeout(() => {
        // Redirect to main app - try the most common port first
        const redirectUrl = `http://localhost:5173/?admin=true&location=${encodeURIComponent(selectedLocation)}`;
        window.location.href = redirectUrl;
      }, 100);
    }
  };

  const handleHomeClick = async () => {
    try {
      // Call the backend to handle admin logout
      const response = await fetch('http://localhost:3003/api/admin/logout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>'
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Admin logout successful:', data.message);
      } else {
        console.log('⚠️ Admin logout request failed, proceeding with local cleanup');
      }
    } catch (error) {
      console.log('⚠️ Admin logout service error, proceeding with local cleanup:', error);
    }

    // Clear local storage
    localStorage.removeItem('userLocation');
    localStorage.removeItem('isAdmin');
    setSelectedLocation('');

    // Dispatch event to notify main app of admin status change
    window.dispatchEvent(new Event('adminStatusChanged'));

    console.log('👑 Admin logged out - Data collection stopped');
  };

  return (
    <div className="location-selection-container">
      <section className="hero">
        <div className="search-container">
          <button
            className="home-icon-btn"
            onClick={handleHomeClick}
          >
            <img
              src="/home.png"
              alt="Home"
              className="home-icon"
            />
          </button>
          
          <select
            className="location-dropdown"
            value={selectedLocation}
            onChange={handleLocationChange}
          >
            <option value="">Select Location</option>
            <option value="deans-office">Dean's Office</option>
            <option value="lecture-hall">Lecture Hall</option>
            <option value="basement">Basement</option>
          </select>
          
          {selectedLocation && (
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                padding: '0.5rem 1rem',
                background: 'rgba(255, 255, 255, 0.9)',
                borderRadius: '8px',
                color: '#011c40',
                fontWeight: 'bold'
              }}>
                Selected: {getLocationDisplayName(selectedLocation)}
              </div>
              <button
                className="continue-button"
                onClick={handleSubmit}
              >
                Continue to Home
              </button>
            </div>
          )}
        </div>
        
        <h1><b>Welcome to the Smart Indoor Air Quality Monitoring System</b></h1>
        <img src="/logo.jpg" alt="AirSense Logo" className="logo" />
        <p>Track, analyze, and improve indoor air quality with ease</p>
        
        <div className="admin-info">
          <p>👑 Admin Access - Select a location to monitor</p>
          <p style={{ fontSize: '0.9rem', margin: '0.5rem 0 0 0', opacity: 0.8 }}>
            Click home icon in main app to return here
          </p>
        </div>
      </section>
    </div>
  );
};

export default LocationSelection;
